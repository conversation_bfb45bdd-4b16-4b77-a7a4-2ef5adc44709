"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./mobView.module.css";

import Modal from "react-modal";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useWebsocketContext } from "@/app/context/AuthContext";
import RecipientConfirmModal from "../../components/RecipientConfirmModal/page";

const page = ({
  listingId,
  name,
  min_liquidity,
  max_liquidity,
  available_liquidity,
  payIn_option,
  payOut_option,
  rate,
  terms,
  time,
  payIn_currency,
  payOut_currency,
}) => {
  const [modalIsOpen, setIsOpen] = useState(false);
  const [amountExch, setAmountExch] = useState("");
  const [amountExchRecieve, setAmountExchRecieve] = useState("");
  const [inputValidation, setInputValidation] = useState("");
  const [orderId, setOrderId] = useState(null);
  const [showOnce, setShowOnce] = useState(false);
  const [modalIsOpen2, setModalIsOpen2] = useState(false);

  const router = useRouter();
  const timestamp = time;

  const date = new Date(timestamp);

  const datePart = `${date.getFullYear()}-${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date
    .getHours()
    .toString()
    .padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date
    .getSeconds()
    .toString()
    .padStart(2, "0")}`;

  const customStyles = {
    content: {
      width: "95%",
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "16px",
      height: "85vh",
      maxHeight: "90vh",
      overflow: "auto",
      zIndex: 9999,
      position: "fixed",
    },
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.6)",
      zIndex: 9998,
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backdropFilter: "blur(4px)",
    },
  };

  const authTokenRef = useRef(null);
  if (typeof window !== "undefined") {
    const token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  let verifyStatus;
  if (typeof window !== "undefined") {
    verifyStatus = localStorage.getItem("verificationStatus");
  }
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  const {
    connection,
    messageHistory,
    sendMessage,
    recentMessage,
    lastJsonMessage,
  } = useWebsocketContext();

  const dataValue = lastJsonMessage;
  const singleRef = useRef(false); // Initialize the ref
  const singleRef1 = useRef(false); // Initialize the ref

  // To track if effect has run already

  useEffect(() => {
    const dataValue = lastJsonMessage;

    if (dataValue?.error && listingId && modalIsOpen) {
      toast.error(dataValue?.error);
    }

    if (dataValue?.error === "Trade request already sent") {
      setOrderId(dataValue?.data?.order_id);
      toast.error("Trade request already sent");
    } else if (dataValue?.data?.order_id && modalIsOpen) {
      setOrderId(dataValue?.data?.order_id);
      // Toast will be handled by SSE/notification system - no duplicate needed
      // if (showOnce == true) {
      //   toast.success(dataValue?.data?.message);
      //   setShowOnce(true);
      // }
    }
  }, [lastJsonMessage]);

  const handleSendWebsocketMsg = () => {
    const payload = {
      action: "send_request",
      listing_id: Number(listingId),
      trade_amount: Number(amountExch),
    };

    sendMessage(JSON.stringify(payload));
  };

  const createTradeRequest = () => {
    if (payOut_currency === "USDT") {
      try {
        handleSendWebsocketMsg();
      } catch (error) {
        console.log(error);
      }
    } else {
      setModalIsOpen2(true);
    }
  };

  useEffect(() => {
    if (orderId) {
      const timeoutId = setTimeout(() => {
        router.push(`/pages/trade/${orderId}?type=user`);
      }, 700); // Use 700 as a number, not an array

      return () => {
        clearTimeout(timeoutId); // Clear the timeout properly
      };
    }
  }, [orderId]);

  const openModal = (event) => {
    // event.stopPropagation();
    setIsOpen(!modalIsOpen);
  };
  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
    // subtitle.style.color = null;
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  Modal.setAppElement("body");
  let subtitle;

  //Modal exhange api

  // const modalRateExch = async () => {
  //   try {
  //     const res = await exchangeRateCurrencyApi(
  //       payIn_currency,
  //       payOut_currency,
  //       amountExch,
  //       listingId
  //     );
  //     console.log(res.data);
  //     setAmountExchRecieve(res.data.data.return_amount);
  //   } catch (error) {
  //     console.error(error);
  //   }
  // };
  const modalRateExch = async () => {
    if (!modalIsOpen) {
      return;
    }

    try {
      const res = await customFetchWithToken.get(
        `/current-currency-rate/?from_currency=${payIn_currency}&to_currency=${payOut_currency}&amount=${amountExch}&listing_id=${listingId}`
      );

      setAmountExchRecieve(res.data.data.return_amount.toFixed(2));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmitForTrade = (e) => {
    e.preventDefault();

    if (!amountExch) {
      toast.error(`Input ${payIn_currency} amount to trade `);
      return;
    }

    router.push(
      `/pages/trade?name=${name}&available_liquidity=${available_liquidity}&min_liquidity=${min_liquidity}&max_liquidity=${max_liquidity}&payIn_option=${payIn_option}&payOut_option=${payOut_option}&rate=${rate}&terms=${terms}&time=${datePart}&payIn_currency=${payIn_currency}&payOut_currency=${payOut_currency}`
    );
  };

  const handleSetAmountExch = (e) => {
    const value = e.target.value;

    if (value < 1 || value.length > 20) {
      setAmountExch("");
    } else if (value > max_liquidity) {
      setInputValidation("Value should be less than Maximum Liquidity");
      setAmountExch("");
    } else if (value < min_liquidity) {
      setInputValidation("Value should be more than Minimum Liquidity");
      setAmountExch(e.target.value);
    } else {
      setAmountExch(e.target.value);
      setInputValidation("");
    }
  };

  const handleBuyFunc = () => {
    if (verifyStatus === "User_Detail") {
      router.push("/verification/personaldetails");
    } else if (verifyStatus === "Document-Verifications") {
      router.push("/verification/status");
    } else if (!token) {
      router.push("/");
    } else if (verifyStatus === "Dash_Board") {
      setIsOpen(!modalIsOpen);
    }
  };

  useEffect(() => {
    modalRateExch();
  }, [amountExch]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (modalIsOpen) {
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "unset";
      document.body.style.position = "unset";
      document.body.style.width = "unset";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset";
      document.body.style.position = "unset";
      document.body.style.width = "unset";
    };
  }, [modalIsOpen]);

  //Modal exhange api

  return (
    <div className={styles.container}>
      {/* Trader Header */}
      <div className={styles.name}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={styles.upload} width="24" height="24">
          <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" />
        </svg>
        <span>{name}</span>
      </div>
      {/* Listing ID */}
      <div className={styles.detailsInfo}>Listing ID: {listingId || "N/A"}</div>
      {/* Trader Stats */}
      <div className={styles.orderInfo}>
        <div className={styles.subClassInfo}>
          <div className={styles.order}>47 Trades</div>
          <div className={styles.orderCompleteion}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 9 8"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.97786 0.673541C5.77194 0.623275 5.55471 0.690988 5.41661 0.848453L3.16856 3.41181V7.34283H7.02827C7.29538 7.34283 7.52534 7.15858 7.57774 6.90256L8.11565 4.27388C8.11565 4.27388 8.11565 4.27389 8.11565 4.27388C8.18499 3.935 7.91979 3.61884 7.56619 3.61884H5.52193C5.39795 3.61884 5.28402 3.55214 5.22552 3.4453C5.16701 3.33845 5.17343 3.20881 5.2422 3.10798L6.32959 1.51386C6.54451 1.19874 6.37516 0.770626 5.99924 0.678773L5.97786 0.673541C5.97789 0.673549 5.97782 0.673532 5.97786 0.673541ZM2.49617 7.34283V3.61885L1.37552 3.61884V7.34283H2.49617ZM2.67774 2.96167L4.90609 0.420772C5.20996 0.0742817 5.68802 -0.0746649 6.14102 0.0360107L6.16232 0.0412219C6.16229 0.0412141 6.16235 0.0412296 6.16232 0.0412219C6.98929 0.24335 7.36185 1.18517 6.88907 1.87837L6.15013 2.96167H7.56619C8.34409 2.96167 8.92753 3.65722 8.77499 4.40276L8.23707 7.03145C8.12181 7.59463 7.61591 8 7.02827 8H1.03932C0.853645 8 0.703125 7.85289 0.703125 7.67141V3.29025C0.703125 3.20311 0.738546 3.11953 0.801595 3.05791C0.864644 2.99628 0.950158 2.96167 1.03932 2.96167L2.67774 2.96167Z"
                fill="#059669"
              />
            </svg>
            100% Success
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className={styles.bodyWrapper}>
        {/* Exchange Rate Highlight */}
        <div className={styles.rateHighlight}>
          1 {payIn_currency || "USDT"} ={" "}
          {rate && !isNaN(Number(rate)) ? Number(rate).toFixed(2) : "N/A"}{" "}
          {payOut_currency || "INR"}
        </div>

        {/* Liquidity Information */}
        <div className={styles.liquidityWrapper}>
          <div>
            <span className={styles.grey}>Available:</span>
            <span>
              {available_liquidity || "N/A"} {payIn_currency || "USDT"}
            </span>
          </div>
          <div>
            <span className={styles.grey}>Range:</span>
            <span>
              {min_liquidity || "N/A"} - {max_liquidity || "N/A"}{" "}
              {payIn_currency || "USDT"}
            </span>
          </div>
        </div>

        {/* Currency Exchange Flow */}
        <div className={styles.currencyWrapper}>
          <div>
            {payIn_currency || "USDT"}
            <svg
              width={12}
              height={12}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 448 512"
            >
              <path
                fill="#64748b"
                d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"
              />
            </svg>
            {payOut_currency || "INR"}
            <span className={styles.payinVerticalLine2}></span>
          </div>

          {/* Payment Methods */}
          <div className={styles.payoutOption}>
            Pay with: {payIn_option || "N/A"}
            <span className={styles.payinVerticalLine}></span>
          </div>

          <div className={styles.payoutOption}>
            Receive via: {payOut_option || "N/A"}
            <span className={styles.payinVerticalLine1}></span>
          </div>
        </div>
      </div>
      {/* Action Button */}
      <div className={styles.bottomCont}>
        <div className={styles.buyBtnWrapper} onClick={handleBuyFunc}>
          <button className={styles.buyBtn}>
            Buy {payOut_currency || "INR"}
          </button>
        </div>
      </div>
      <Modal
        isOpen={modalIsOpen}
        onAfterOpen={afterOpenModal}
        onRequestClose={closeModal}
        style={customStyles}
        contentLabel="Example Modal"
      >
        <div className={styles.modalCont} onClick={(e) => e.stopPropagation()}>
          <div className={styles.modalTop}>
            <div className={styles.left}>
              <div className={styles.modalProfileWrapper}>
                <div className={styles.modalPic}>
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="48" height="48">
                      <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" />
                    </svg>
                    <span className={styles.modalName}>{name}</span>
                  </div>
                  <div className={styles.right} onClick={closeModal}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 15 15"
                      fill="none"
                    >
                      <path
                        d="M8.60216 7.483L14.8197 1.28213C14.9425 1.13904 15.0067 0.954975 14.9994 0.766723C14.9921 0.57847 14.9139 0.399895 14.7803 0.266681C14.6468 0.133468 14.4677 0.0554274 14.2789 0.048156C14.0902 0.0408846 13.9056 0.104918 13.7622 0.227459L7.54466 6.42833L1.32716 0.219979C1.18593 0.0791288 0.994384 0 0.794658 0C0.594931 0 0.403385 0.0791288 0.262158 0.219979C0.12093 0.360829 0.0415888 0.551863 0.0415888 0.751055C0.0415888 0.950247 0.12093 1.14128 0.262158 1.28213L6.48716 7.483L0.262158 13.6839C0.183646 13.7509 0.119881 13.8334 0.0748636 13.9262C0.0298461 14.019 0.00454851 14.1201 0.000558893 14.2231C-0.00343073 14.3261 0.0139734 14.4289 0.0516793 14.5249C0.0893852 14.6208 0.146579 14.708 0.21967 14.7809C0.292761 14.8538 0.380171 14.9109 0.476415 14.9485C0.572659 14.9861 0.675658 15.0034 0.778948 14.9994C0.882237 14.9955 0.983586 14.9702 1.07664 14.9253C1.16968 14.8804 1.25242 14.8168 1.31966 14.7385L7.54466 8.53767L13.7622 14.7385C13.9056 14.8611 14.0902 14.9251 14.2789 14.9178C14.4677 14.9106 14.6468 14.8325 14.7803 14.6993C14.9139 14.5661 14.9921 14.3875 14.9994 14.1993C15.0067 14.011 14.9425 13.827 14.8197 13.6839L8.60216 7.483Z"
                        fill="#64748b"
                      />
                    </svg>
                  </div>
                </div>

                <div className={styles.orderContaier}>
                  <span>Last seen 18 mins ago</span>
                </div>

                <div className={styles.orderContaier}>
                  <div className={styles.orders}>47 Orders</div>
                  <div className={styles.orders}>100% Success</div>
                  <div className={styles.orders}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 9 8"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M5.85286 0.673541C5.64694 0.623275 5.42971 0.690988 5.29161 0.848453L3.04356 3.41181V7.34283H6.90327C7.17038 7.34283 7.40034 7.15858 7.45274 6.90256L7.99065 4.27388C7.99065 4.27388 7.99065 4.27389 7.99065 4.27388C8.05999 3.935 7.79479 3.61884 7.44119 3.61884H5.39693C5.27295 3.61884 5.15902 3.55214 5.10052 3.4453C5.04201 3.33845 5.04843 3.20881 5.1172 3.10798L6.20459 1.51386C6.41951 1.19874 6.25016 0.770626 5.87424 0.678773L5.85286 0.673541C5.85289 0.673549 5.85282 0.673532 5.85286 0.673541ZM2.37117 7.34283V3.61885L1.25052 3.61884V7.34283H2.37117ZM2.55274 2.96167L4.78109 0.420772C5.08496 0.0742817 5.56302 -0.0746649 6.01602 0.0360107L6.03732 0.0412219C6.03729 0.0412141 6.03735 0.0412296 6.03732 0.0412219C6.86429 0.24335 7.23685 1.18517 6.76407 1.87837L6.02513 2.96167H7.44119C8.21909 2.96167 8.80253 3.65722 8.64999 4.40276L8.11207 7.03145C7.99681 7.59463 7.49091 8 6.90327 8H0.914321C0.728645 8 0.578125 7.85289 0.578125 7.67141V3.29025C0.578125 3.20311 0.613546 3.11953 0.676595 3.05791C0.739644 2.99628 0.825158 2.96167 0.914323 2.96167L2.55274 2.96167Z"
                        fill="#059669"
                      />
                    </svg>
                    95.76%
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.modalBottom}>
            <div className={styles.leftB}>
              <div className={styles.info}>
                <div className={styles.infoPoints}>
                  <span>Exchange Rate:</span>
                  <span className={styles.highlight}>
                    {rate && !isNaN(Number(rate))
                      ? Number(rate).toFixed(2)
                      : "N/A"}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Available:</span>
                  <span className={styles.highlight}>
                    {available_liquidity || "N/A"} {payIn_currency || "USDT"}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Min Amount:</span>
                  <span className={styles.highlight}>
                    {min_liquidity || "N/A"} {payIn_currency || "USDT"}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Max Amount:</span>
                  <span className={styles.highlight}>
                    {max_liquidity || "N/A"} {payIn_currency || "USDT"}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Listed:</span>
                  <span className={styles.highlight}>{datePart}</span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Time Limit:</span>
                  <span className={styles.highlight}>15 Minutes</span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Pay Method:</span>
                  <span className={styles.highlight}>
                    {payIn_option || "N/A"}
                  </span>
                </div>
                <div className={styles.infoPoints}>
                  <span>Receive Method:</span>
                  <span className={styles.highlight}>
                    {payOut_option || "N/A"}
                  </span>
                </div>
              </div>

              <div className={styles.terms}>
                <div className={styles.tHeader}>Terms & Conditions</div>
                <div className={styles.termsPara}>
                  {terms || "No specific terms provided"}
                </div>
              </div>
            </div>

            <div className={styles.rightB}>
              <div>
                <label>I want to pay</label>
                <div className={styles.validationCheck}>{inputValidation}</div>
                <div className={styles.payInput}>
                  <input
                    type="number"
                    placeholder={`${min_liquidity || 0} - ${
                      max_liquidity || 0
                    }`}
                    onChange={handleSetAmountExch}
                    value={amountExch}
                    required
                    min={min_liquidity}
                    max={max_liquidity}
                  />
                  <span>{payIn_currency || "USDT"}</span>
                </div>
              </div>

              <div>
                <label>I will receive</label>
                <div className={styles.payInput}>
                  <div className={styles.receive}>
                    {amountExchRecieve || "0.00"}
                  </div>
                  <span>{payOut_currency}</span>
                </div>
              </div>

              <button
                className={styles.buttonBBUY}
                onClick={createTradeRequest}
              >
                Buy {payOut_currency || "INR"}
              </button>

              {modalIsOpen2 && (
                <RecipientConfirmModal
                  payOut_currency={payOut_currency}
                  modalIsOpen2={modalIsOpen2}
                  setModalIsOpen2={setModalIsOpen2}
                  enteredAmount={amountExch}
                  payOut_option={payOut_option}
                  listingId={listingId}
                  name={name}
                  min_liquidity={min_liquidity}
                  max_liquidity={max_liquidity}
                  available_liquidity={available_liquidity}
                  payIn_option={payIn_option}
                  rate={rate}
                  terms={terms}
                  time={time}
                  payIn_currency={payIn_currency}
                />
              )}
            </div>
          </div>
        </div>
      </Modal>{" "}
    </div>
  );
};

export default page;
